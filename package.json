{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cloudinary": "^1.41.3", "connect-flash": "^0.1.1", "dotenv": "^17.2.0", "ejs": "^3.1.10", "ejs-mate": "^4.0.0", "express": "^5.1.0", "express-session": "^1.18.1", "joi": "^17.13.3", "method-override": "^3.0.0", "mongoose": "^8.16.1", "multer": "^2.0.2", "multer-storage-cloudinary": "^4.0.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0"}}
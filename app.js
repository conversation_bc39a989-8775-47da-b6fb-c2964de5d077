if (process.env.NODE_ENV !== "production") {
  require('dotenv').config(); 
}


const express = require("express");
const app = express();
const mongoose = require("mongoose");
const path = require("path");
const methodOverride = require("method-override");
const ejsMate = require("ejs-mate");
const listingRouter = require("./routes/listing.js");
const reviewRouter = require("./routes/reveiw.js");
const session = require("express-session");
const flash = require("connect-flash");
const userRouter = require("./routes/user.js")
const passport = require("passport");

//const { Http2ServerRequest } = require("http2");
// const wrapAsync = require("./utils/wrapAsync.js");
// const ExpressError = require("./utils/ExpressError.js");
// const { listingSchema,reviewSchema } = require("./schema.js");
// const Review = require("./models/review.js");
// const Listing = require("./models/listing.js");

const sessionOptions = {
  secret: process.env.SESSION_SECRET || "mysecret",
  resave: false,                  // ✅ boolean, not string
  saveUninitialized: false,       // ✅ correct spelling and value
  cookie: {
    expires: Date.now() + 7 * 24 * 60 * 60 * 1000,
    maxAge: 7 * 24 * 60 * 60 * 1000,
    httpOnly: true,
  },
};

// MongoDB Connection
const mongo_url = process.env.MONGODB_URI || "mongodb://127.0.0.1:27017/wanderlust";
main()
  .then(() => console.log("Connected to DB"))
  .catch((err) => console.log(err));
async function main() {
  await mongoose.connect(mongo_url);
}

// View Engine Setup
app.engine("ejs", ejsMate);
app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));

// Middleware
app.use(express.urlencoded({ extended: true }));
app.use(methodOverride("_method"));
app.use(express.static(path.join(__dirname, "public")));

app.use(session(sessionOptions));
app.use(flash());
app.use(passport.initialize());
app.use(passport.session());

// Root Route
app.get("/", (req, res) => {
  res.send("Hi, I am root here");
});

const LocalStrategy = require("passport-local");
const User = require("./models/user");



passport.use(new LocalStrategy(User.authenticate()));
passport.serializeUser(User.serializeUser());
passport.deserializeUser(User.deserializeUser());
// Routes


app.use((req,res,next)=>{
  res.locals.success = req.flash("success");
  res.locals.error = req.flash("error");
  res.locals.currentUser = req.user; 
  next();
});
// // Index route
// app.get("/listings", wrapAsync(async (req, res) => {
//   const allListings = await Listing.find({});
//   res.render("listings/index", { allListings });
// }));

// // New route (form)
// app.get("/listings/new", (req, res) => {
//   res.render("listings/new");
// });

// // Show route
// app.get("/listings/:id", wrapAsync(async (req, res) => {
//   const { id } = req.params;
//   const listing = await Listing.findById(id).populate("reviews");
//   if (!listing) throw new ExpressError(404, "Listing not found");
//   res.render("listings/show", { listing });
// }));

// // Create route
// app.post("/listings", validateListing, wrapAsync(async (req, res) => {
//   const newListing = new Listing(req.body.listing);
//   await newListing.save();
//   res.redirect("/listings");
// }));

// // Edit route
// app.get("/listings/:id/edit", wrapAsync(async (req, res) => {
//   const { id } = req.params;
//   const listing = await Listing.findById(id);
//   if (!listing) throw new ExpressError(404, "Listing not found");
//   res.render("listings/edit", { listing });
// }));

// // Update route
// app.put("/listings/:id", validateListing, wrapAsync(async (req, res) => {
//   const { id } = req.params;
//   await Listing.findByIdAndUpdate(id, { ...req.body.listing });
//   res.redirect(`/listings/${id}`);
// }));

// // Delete route
// app.delete("/listings/:id", wrapAsync(async (req, res) => {
//   const { id } = req.params;
//   await Listing.findByIdAndDelete(id);
//   res.redirect("/listings");
// }));


app.use("/listings",listingRouter);
app.use("/listings/:id/reviews",reviewRouter);
app.use("/",userRouter);



// //post review
// app.post("/listings/:id/reviews",validateReview,wrapAsync(async(req,res)=>{
//     let listing = await Listing.findById(req.params.id);
//     let newReview = new Review(req.body.review);
//     listing.reviews.push(newReview);
//     await newReview.save();
//     await listing.save();
//     console.log("new reivew saved");
//     res.redirect(`/listings/${listing._id}`);
// }));

// //delete refviews
// app.delete("/listings/:id/reviews/:reviewId",wrapAsync(async(req,res)=>{
//   let{id,reviewId} = req.params;
//   await Listing.findByIdAndUpdate(id, {$pull:{reviews:reviewId}});
//   await Review.findByIdAndDelete(reviewId);
//   res.redirect(`/listings/${id}`);
// }))

// 404 Route Handler
// app.all("*", (req, res, next) => {
//   next(new ExpressError(404, "Page not found"));
// });

// Error Middleware
app.use((err, req, res, next) => {
  const { statusCode = 500 } = err;
  if (!err.message) err.message = "Something went wrong";
  res.status(statusCode).render("error", { err });
});

// Start Server
const PORT = process.env.PORT || 8080;
app.listen(PORT, () => {
  console.log(`Server is listening on port ${PORT}`);
});

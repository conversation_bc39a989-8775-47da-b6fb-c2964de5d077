# Wanderlust - Full Stack Listing Application

A full-stack web application for managing property listings with user authentication, reviews, and image uploads.

## Features

- **User Authentication**: Secure login/signup with Passport.js
- **Property Listings**: Create, read, update, and delete property listings
- **Image Upload**: Cloudinary integration for image storage
- **Reviews System**: Users can add and delete reviews for listings
- **Authorization**: Owner-based permissions for listings and reviews
- **Responsive Design**: Mobile-friendly interface

## Tech Stack

- **Backend**: Node.js, Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: Passport.js with Local Strategy
- **File Upload**: Multer with Cloudinary storage
- **Template Engine**: EJS with EJS-Mate
- **Validation**: Joi schema validation
- **Session Management**: Express-session with connect-flash

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (local or cloud instance)
- Cloudinary account for image storage

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd fulllst
```

2. Install dependencies:
```bash
npm install
```

3. Create environment variables:
Create a `.env` file in the root directory with:
```env
CLOUD_NAME=your_cloudinary_cloud_name
CLOUD_API_KEY=your_cloudinary_api_key
CLOUD_API_SECRET=your_cloudinary_api_secret
MONGODB_URI=your_mongodb_connection_string
SESSION_SECRET=your_session_secret_key
```

4. Start the application:
```bash
# Development
npm run dev

# Production
npm start
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `CLOUD_NAME` | Cloudinary cloud name | Yes |
| `CLOUD_API_KEY` | Cloudinary API key | Yes |
| `CLOUD_API_SECRET` | Cloudinary API secret | Yes |
| `MONGODB_URI` | MongoDB connection string | Yes |
| `SESSION_SECRET` | Secret key for session encryption | Yes |
| `NODE_ENV` | Environment (development/production) | No |
| `PORT` | Server port (default: 8080) | No |

## Deployment

### Vercel Deployment

1. Install Vercel CLI:
```bash
npm i -g vercel
```

2. Deploy:
```bash
vercel
```

3. Set environment variables in Vercel dashboard

### Environment Setup for Production

- Use MongoDB Atlas for cloud database
- Set strong SESSION_SECRET (32+ characters)
- Configure Cloudinary for image storage
- Set NODE_ENV=production

## API Routes

- `GET /` - Home page
- `GET /listings` - View all listings
- `GET /listings/new` - Create new listing form
- `POST /listings` - Create new listing
- `GET /listings/:id` - View specific listing
- `GET /listings/:id/edit` - Edit listing form
- `PUT /listings/:id` - Update listing
- `DELETE /listings/:id` - Delete listing
- `POST /listings/:id/reviews` - Add review
- `DELETE /listings/:id/reviews/:reviewId` - Delete review
- `GET /signup` - User registration
- `POST /signup` - Create user account
- `GET /login` - User login
- `POST /login` - Authenticate user
- `GET /logout` - User logout

## Security Features

- Input validation with Joi schemas
- Authentication middleware
- Authorization checks for owners
- Secure session management
- Environment variable protection
- CSRF protection ready

## Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## License

ISC License
